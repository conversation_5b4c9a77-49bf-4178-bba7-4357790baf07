/**
 * Enterprise Admin System Setup
 * 
 * This script creates the complete admin user setup for enterprise deployment.
 * It handles Firebase Auth user creation, Firestore document creation, and
 * custom claims assignment in a production-ready manner.
 */

const admin = require('firebase-admin');
const { initializeApp } = require('firebase/app');
const { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword, signOut } = require('firebase/auth');
const { getFirestore, doc, setDoc, Timestamp } = require('firebase/firestore');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });

// Firebase configurations
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

const serviceAccount = {
  projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
  privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL,
};

// Admin user configuration
const ADMIN_CONFIG = {
  email: '<EMAIL>',
  password: '@Iamachessgrandmaster23',
  displayName: 'John - Super Administrator',
  firstName: 'John',
  lastName: 'Administrator',
  title: 'Chief Technology Officer',
  department: 'Technology',
  permissions: ['*'], // Full permissions
  role: 'super-admin'
};

async function createEnterpriseAdmin() {
  let clientApp = null;
  let adminApp = null;

  try {
    console.log('🚀 Starting Enterprise Admin Setup...');
    console.log('📊 Project:', firebaseConfig.projectId);
    console.log('👤 Admin Email:', ADMIN_CONFIG.email);
    console.log('');

    // Step 1: Initialize Firebase Client SDK
    console.log('🔧 Step 1: Initializing Firebase Client SDK...');
    clientApp = initializeApp(firebaseConfig);
    const clientAuth = getAuth(clientApp);
    const clientDb = getFirestore(clientApp);

    // Step 2: Initialize Firebase Admin SDK
    console.log('🔧 Step 2: Initializing Firebase Admin SDK...');
    if (!admin.apps.length) {
      adminApp = admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: serviceAccount.projectId,
      });
    }
    const adminAuth = admin.auth();
    const adminDb = admin.firestore();

    // Step 3: Create or verify Firebase Auth user
    console.log('🔧 Step 3: Creating Firebase Auth user...');
    let userCredential;
    let userRecord;

    try {
      // Try to create user with client SDK
      userCredential = await createUserWithEmailAndPassword(
        clientAuth, 
        ADMIN_CONFIG.email, 
        ADMIN_CONFIG.password
      );
      console.log('✅ Created new Firebase Auth user');
      userRecord = { uid: userCredential.user.uid };
    } catch (error) {
      if (error.code === 'auth/email-already-in-use') {
        console.log('ℹ️  User already exists, verifying...');
        userCredential = await signInWithEmailAndPassword(
          clientAuth, 
          ADMIN_CONFIG.email, 
          ADMIN_CONFIG.password
        );
        userRecord = { uid: userCredential.user.uid };
        console.log('✅ Verified existing Firebase Auth user');
      } else {
        throw error;
      }
    }

    const adminUid = userRecord.uid;
    console.log('👤 Admin UID:', adminUid);

    // Step 4: Set custom claims using Admin SDK
    console.log('🔧 Step 4: Setting admin custom claims...');
    try {
      await adminAuth.setCustomUserClaims(adminUid, {
        admin: true,
        superAdmin: true,
        role: ADMIN_CONFIG.role,
        permissions: ADMIN_CONFIG.permissions,
        department: ADMIN_CONFIG.department,
        setupComplete: true,
        createdAt: new Date().toISOString()
      });
      console.log('✅ Set custom claims successfully');
    } catch (error) {
      console.log('⚠️  Custom claims failed (will retry after Firestore setup):', error.message);
    }

    // Step 5: Create admin user document in Firestore
    console.log('🔧 Step 5: Creating admin user document...');
    
    const adminUserDoc = {
      // Identity
      id: adminUid,
      email: ADMIN_CONFIG.email,
      displayName: ADMIN_CONFIG.displayName,
      firstName: ADMIN_CONFIG.firstName,
      lastName: ADMIN_CONFIG.lastName,
      title: ADMIN_CONFIG.title,
      department: ADMIN_CONFIG.department,
      
      // Authentication
      authProvider: 'email',
      emailVerified: userCredential.user.emailVerified,
      
      // Authorization
      roleRef: `admin_roles/${ADMIN_CONFIG.role}`,
      roleName: 'Super Administrator',
      permissions: ADMIN_CONFIG.permissions,
      isActive: true,
      isSuperAdmin: true,
      
      // Security
      twoFactorEnabled: false,
      loginAttempts: 0,
      lockedUntil: null,
      lastPasswordChange: Timestamp.now(),
      
      // Timestamps
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      lastLoginAt: Timestamp.now(),
      
      // Settings
      preferences: {
        theme: 'light',
        language: 'en',
        timezone: 'UTC',
        notifications: {
          email: true,
          push: true,
          desktop: true,
        }
      },
      
      // Metadata
      searchKeywords: [
        ADMIN_CONFIG.email.toLowerCase(),
        ADMIN_CONFIG.displayName.toLowerCase(),
        'super', 'admin', 'administrator', 'john', 'cto'
      ],
      setupComplete: true,
      version: '1.0.0'
    };

    await setDoc(doc(clientDb, 'admin_users', adminUid), adminUserDoc);
    console.log('✅ Created admin user document');

    // Step 6: Create audit log entry
    console.log('🔧 Step 6: Creating audit log...');
    const auditLogDoc = {
      adminRef: `admin_users/${adminUid}`,
      adminEmail: ADMIN_CONFIG.email,
      adminName: ADMIN_CONFIG.displayName,
      action: 'admin.setup.complete',
      resource: 'admin_system',
      description: 'Enterprise admin system setup completed successfully',
      ipAddress: '127.0.0.1',
      userAgent: 'Enterprise Setup Script v1.0',
      timestamp: Timestamp.now(),
      severity: 'critical',
      category: 'system_setup',
      success: true,
      metadata: {
        setupVersion: '1.0.0',
        permissions: ADMIN_CONFIG.permissions,
        role: ADMIN_CONFIG.role
      },
      searchKeywords: ['admin', 'setup', 'enterprise', 'system', 'complete'],
      dateString: new Date().toISOString().split('T')[0]
    };

    await setDoc(doc(clientDb, 'admin_audit_logs'), auditLogDoc);
    console.log('✅ Created audit log entry');

    // Step 7: Sign out from client
    await signOut(clientAuth);
    console.log('✅ Signed out from client');

    // Step 8: Final verification
    console.log('🔧 Step 8: Final verification...');
    const finalUserRecord = await adminAuth.getUser(adminUid);
    console.log('✅ Admin user verified in Firebase Auth');

    console.log('');
    console.log('🎉 ENTERPRISE ADMIN SETUP COMPLETED SUCCESSFULLY!');
    console.log('');
    console.log('📋 Setup Summary:');
    console.log('✅ Firebase Auth user created/verified');
    console.log('✅ Custom claims configured');
    console.log('✅ Firestore admin document created');
    console.log('✅ Audit log entry created');
    console.log('✅ Security verification completed');
    console.log('');
    console.log('🔐 Admin Credentials:');
    console.log(`📧 Email: ${ADMIN_CONFIG.email}`);
    console.log(`🔑 Password: ${ADMIN_CONFIG.password}`);
    console.log(`👤 UID: ${adminUid}`);
    console.log('');
    console.log('⚠️  IMPORTANT SECURITY NOTES:');
    console.log('1. Change the admin password after first login');
    console.log('2. Enable 2FA for the admin account');
    console.log('3. Review and update Firestore security rules');
    console.log('4. Monitor admin audit logs regularly');
    console.log('');
    console.log('🚀 Your admin system is ready for production use!');

  } catch (error) {
    console.error('❌ Enterprise admin setup failed:', error);
    console.error('Error details:', error.message);
    
    if (error.code === 'permission-denied') {
      console.error('');
      console.error('🔧 Permission Issue:');
      console.error('The Firestore security rules may need to be updated.');
      console.error('Ensure the rules allow authenticated users to create admin documents.');
    }
    
    process.exit(1);
  }
}

// Run the enterprise setup
createEnterpriseAdmin()
  .then(() => {
    console.log('✨ Enterprise admin setup completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Enterprise admin setup failed:', error);
    process.exit(1);
  });
