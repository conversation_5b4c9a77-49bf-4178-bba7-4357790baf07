/**
 * Authentication Guard Component
 * Protects admin routes using shared authentication
 */

'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAdminAuth } from '@encreasl/auth';

interface AuthGuardProps {
  children: React.ReactNode;
  requireAdmin?: boolean;
  requireSuperAdmin?: boolean;
  requiredPermissions?: string[];
}

export function AuthGuard({ 
  children, 
  requireAdmin = true,
  requireSuperAdmin = false,
  requiredPermissions = []
}: AuthGuardProps) {
  const router = useRouter();
  const { 
    user, 
    loading, 
    isAdmin, 
    isSuperAdmin, 
    hasPermission, 
    sessionValid 
  } = useAdminAuth();

  useEffect(() => {
    // Don't redirect while loading
    if (loading) return;

    // Redirect to login if not authenticated
    if (!user || !sessionValid) {
      router.push('/login');
      return;
    }

    // Check admin requirement
    if (requireAdmin && !isAdmin) {
      router.push('/login');
      return;
    }

    // Check super admin requirement
    if (requireSuperAdmin && !isSuperAdmin) {
      router.push('/admin'); // Redirect to regular admin dashboard
      return;
    }

    // Check specific permissions
    if (requiredPermissions.length > 0) {
      const hasAllPermissions = requiredPermissions.every(permission => 
        hasPermission(permission)
      );
      
      if (!hasAllPermissions) {
        router.push('/admin'); // Redirect to main dashboard
        return;
      }
    }
  }, [
    user, 
    loading, 
    isAdmin, 
    isSuperAdmin, 
    sessionValid,
    requireAdmin,
    requireSuperAdmin,
    requiredPermissions,
    hasPermission,
    router
  ]);

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Verifying authentication...</p>
        </div>
      </div>
    );
  }

  // Show access denied if session is invalid
  if (!sessionValid) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Session Expired</h2>
          <p className="text-gray-600 mb-6">Your session has expired. Please sign in again.</p>
          <button
            onClick={() => router.push('/login')}
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md"
          >
            Sign In
          </button>
        </div>
      </div>
    );
  }

  // Show access denied if user doesn't meet requirements
  if (!user || (requireAdmin && !isAdmin) || (requireSuperAdmin && !isSuperAdmin)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
          <p className="text-gray-600 mb-6">
            You don't have permission to access this page.
          </p>
          <button
            onClick={() => router.push('/login')}
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md"
          >
            Sign In
          </button>
        </div>
      </div>
    );
  }

  // Check permissions
  if (requiredPermissions.length > 0) {
    const hasAllPermissions = requiredPermissions.every(permission => 
      hasPermission(permission)
    );
    
    if (!hasAllPermissions) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Insufficient Permissions</h2>
            <p className="text-gray-600 mb-6">
              You don't have the required permissions to access this page.
            </p>
            <button
              onClick={() => router.push('/admin')}
              className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md"
            >
              Go to Dashboard
            </button>
          </div>
        </div>
      );
    }
  }

  // All checks passed, render children
  return <>{children}</>;
}
