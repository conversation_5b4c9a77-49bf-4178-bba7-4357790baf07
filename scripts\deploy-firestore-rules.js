/**
 * Deploy Firestore Security Rules Programmatically
 * 
 * This script uses the Firebase Admin SDK to deploy Firestore security rules
 * that allow authenticated users to create admin documents.
 */

const admin = require('firebase-admin');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });

// Firebase Admin configuration
const serviceAccount = {
  projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
  privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL,
};

// Firestore Security Rules that allow admin user creation
const firestoreRulesSource = `rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // ========================================
    // ADMIN SYSTEM COLLECTIONS
    // ========================================
    
    // Admin Users Collection
    match /admin_users/{adminId} {
      // Allow authenticated users to create their own admin document during setup
      allow create: if request.auth != null && 
                       request.auth.uid == adminId;
      
      // Allow authenticated admins to read/write admin users
      allow read, write: if request.auth != null;
    }
    
    // Admin Audit Logs Collection
    match /admin_audit_logs/{logId} {
      // Allow authenticated users to create audit logs
      allow create: if request.auth != null;
      
      // Allow authenticated admins to read audit logs
      allow read: if request.auth != null;
    }
    
    // System Configuration Collection
    match /system_config/{configId} {
      // Allow authenticated users to read/write system config
      allow read, write: if request.auth != null;
    }
    
    // ========================================
    // MAIN APPLICATION COLLECTIONS
    // ========================================
    
    // Users Collection (for main app users)
    match /users/{userId} {
      // Users can read/write their own data
      allow read, write: if request.auth != null && 
                           request.auth.uid == userId;
      
      // Admins can read all users
      allow read: if request.auth != null;
    }
    
    // Content Collections (posts, pages, etc.)
    match /content/{contentId} {
      // Public read access for published content
      allow read: if resource.data.status == 'published';
      
      // Authenticated users can read all content
      allow read: if request.auth != null;
      
      // Content creators can write their own content
      allow write: if request.auth != null && 
                      (resource == null || 
                       resource.data.authorRef == 'users/' + request.auth.uid);
    }
    
    // ========================================
    // TEMPORARY OPEN RULES FOR SETUP
    // ========================================
    
    // Allow all authenticated users to read/write during setup
    // TODO: Remove this after admin system is fully set up
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}`;

async function deployFirestoreRules() {
  try {
    console.log('🚀 Starting Firestore rules deployment...');
    console.log('📊 Project ID:', serviceAccount.projectId);

    // Initialize Firebase Admin
    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: serviceAccount.projectId,
      });
    }

    console.log('🔧 Deploying Firestore security rules...');

    // Deploy the rules using Admin SDK
    await admin.securityRules().releaseFirestoreRulesetFromSource(firestoreRulesSource);

    console.log('✅ Firestore security rules deployed successfully!');
    console.log('');
    console.log('📋 Rules Summary:');
    console.log('✅ Admin users can create their own documents');
    console.log('✅ Authenticated users can create audit logs');
    console.log('✅ Temporary open access for authenticated users');
    console.log('');
    console.log('⚠️  Important: These are temporary open rules for setup.');
    console.log('   After admin system is set up, update to more restrictive rules.');
    console.log('');
    console.log('🎉 You can now run the admin user creation script!');

  } catch (error) {
    console.error('❌ Error deploying Firestore rules:', error);
    
    if (error.code === 'auth/internal-error' || error.message.includes('PERMISSION_DENIED')) {
      console.error('');
      console.error('🔧 Permission Error Solutions:');
      console.error('1. Ensure your Firebase service account has the correct permissions');
      console.error('2. Grant the "Firebase Rules Admin" role to your service account');
      console.error('3. Grant the "Security Rules Admin" role to your service account');
      console.error('');
      console.error('Service Account:', serviceAccount.clientEmail);
      console.error('Project ID:', serviceAccount.projectId);
    }
    
    process.exit(1);
  }
}

// Run the script
deployFirestoreRules()
  .then(() => {
    console.log('✨ Rules deployment completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Rules deployment failed:', error);
    process.exit(1);
  });
