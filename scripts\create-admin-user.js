/**
 * Create Admin User Script
 * 
 * This script creates the admin user in Firebase Authentication
 * and sets up the necessary Firestore documents.
 */

const admin = require('firebase-admin');
const path = require('path');

// Load environment variables from the root .env.local file
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });

// Firebase Admin configuration
const serviceAccount = {
  projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
  privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL,
};

// Admin user details - YOUR REAL ADMIN ACCOUNT
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = '@Iamachessgrandmaster23';
const ADMIN_NAME = 'John - Admin';

async function createAdminUser() {
  try {
    console.log('🚀 Starting admin user creation...');
    console.log('📊 Project ID:', serviceAccount.projectId);

    // Initialize Firebase Admin
    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: serviceAccount.projectId,
      });
    }

    const auth = admin.auth();
    const db = admin.firestore();

    // Step 1: Create user in Firebase Auth
    console.log('🔧 Creating admin user in Firebase Auth...');
    
    let userRecord;
    try {
      // Try to create the user
      userRecord = await auth.createUser({
        email: ADMIN_EMAIL,
        password: ADMIN_PASSWORD,
        displayName: ADMIN_NAME,
        emailVerified: true,
      });
      
      console.log('✅ Created Firebase Auth user:', ADMIN_EMAIL);
    } catch (error) {
      if (error.code === 'auth/email-already-exists') {
        // User already exists, get their record
        userRecord = await auth.getUserByEmail(ADMIN_EMAIL);
        console.log('ℹ️  Firebase Auth user already exists:', ADMIN_EMAIL);
      } else {
        throw error;
      }
    }

    // Step 2: Set custom claims for admin privileges
    console.log('🔧 Setting admin custom claims...');
    await auth.setCustomUserClaims(userRecord.uid, {
      admin: true,
      superAdmin: true,
      role: 'super-admin',
      permissions: ['*']
    });
    console.log('✅ Set custom claims for admin user');

    // Step 3: Create admin user document in Firestore
    console.log('🔧 Creating admin user document in Firestore...');
    
    const adminUserDoc = {
      id: userRecord.uid,
      email: ADMIN_EMAIL,
      displayName: ADMIN_NAME,
      authProvider: 'email',
      emailVerified: true,
      
      // Role information
      roleRef: 'admin_roles/super-admin',
      roleName: 'Super Admin',
      permissions: ['*'],
      isActive: true,
      isSuperAdmin: true,
      
      // Profile information
      firstName: ADMIN_NAME.split(' ')[0],
      lastName: ADMIN_NAME.split(' ').slice(1).join(' '),
      title: 'Super Administrator',
      
      // Timestamps
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      lastLoginAt: null,
      
      // Settings
      preferences: {
        theme: 'light',
        language: 'en',
        timezone: 'UTC',
        notifications: {
          email: true,
          push: true,
          desktop: true,
        }
      },
      
      // Security
      twoFactorEnabled: false,
      loginAttempts: 0,
      lockedUntil: null,
      
      // Metadata
      searchKeywords: [ADMIN_EMAIL, ADMIN_NAME.toLowerCase(), 'super', 'admin', 'administrator'],
    };

    await db.collection('admin_users').doc(userRecord.uid).set(adminUserDoc);
    console.log('✅ Created admin user document in Firestore');

    // Step 4: Create audit log entry
    console.log('🔧 Creating audit log entry...');
    await db.collection('admin_audit_logs').add({
      adminRef: `admin_users/${userRecord.uid}`,
      adminEmail: ADMIN_EMAIL,
      adminName: ADMIN_NAME,
      action: 'user.create',
      resource: 'admin_users',
      description: 'Super admin user created via setup script',
      ipAddress: '127.0.0.1',
      userAgent: 'Setup Script',
      timestamp: admin.firestore.Timestamp.now(),
      severity: 'high',
      category: 'authentication',
      success: true,
      searchKeywords: ['user', 'create', 'admin', 'setup'],
      dateString: new Date().toISOString().split('T')[0]
    });
    console.log('✅ Created audit log entry');

    console.log('\n🎉 Admin user creation completed successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ Created Firebase Auth user');
    console.log('✅ Set admin custom claims');
    console.log('✅ Created Firestore user document');
    console.log('✅ Created audit log entry');
    
    console.log('\n🔐 Login Credentials:');
    console.log('📧 Email:', ADMIN_EMAIL);
    console.log('🔑 Password:', ADMIN_PASSWORD);
    
    console.log('\n⚠️  Important: Change the password after first login!');

  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    process.exit(1);
  }
}

// Run the script
createAdminUser()
  .then(() => {
    console.log('\n✨ Script completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
