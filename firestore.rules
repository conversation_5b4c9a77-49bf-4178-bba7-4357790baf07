rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // ========================================
    // ADMIN SYSTEM COLLECTIONS
    // ========================================
    
    // Admin Users Collection
    match /admin_users/{adminId} {
      // Allow authenticated users to create their own admin document during setup
      allow create: if request.auth != null && 
                       request.auth.uid == adminId;
      
      // Allow authenticated admins to read/write admin users
      allow read, write: if request.auth != null && 
                           (isAuthenticatedAdmin() || request.auth.uid == adminId);
      
      // Allow reading own profile
      allow read: if request.auth != null && 
                     request.auth.uid == adminId;
    }
    
    // Admin Roles Collection
    match /admin_roles/{roleId} {
      // Allow all authenticated users to read roles (needed for setup)
      allow read: if request.auth != null;
      
      // Allow authenticated admins to write roles
      allow write: if request.auth != null && isAuthenticatedAdmin();
    }
    
    // Admin Sessions Collection
    match /admin_sessions/{sessionId} {
      // Allow authenticated users to create/manage their own sessions
      allow read, write: if request.auth != null && 
                           (resource == null || 
                            resource.data.adminRef == 'admin_users/' + request.auth.uid);
    }
    
    // Admin Audit Logs Collection
    match /admin_audit_logs/{logId} {
      // Allow authenticated users to create audit logs
      allow create: if request.auth != null;
      
      // Allow authenticated admins to read audit logs
      allow read: if request.auth != null && isAuthenticatedAdmin();
      
      // Audit logs are append-only (no updates/deletes)
      allow update, delete: if false;
    }
    
    // System Configuration Collection
    match /system_config/{configId} {
      // Allow authenticated users to read system config
      allow read: if request.auth != null;
      
      // Allow authenticated admins to write system config
      allow write: if request.auth != null && isAuthenticatedAdmin();
    }
    
    // ========================================
    // MAIN APPLICATION COLLECTIONS
    // ========================================
    
    // Users Collection (for main app users)
    match /users/{userId} {
      // Users can read/write their own data
      allow read, write: if request.auth != null && 
                           request.auth.uid == userId;
      
      // Admins can read all users
      allow read: if request.auth != null && isAuthenticatedAdmin();
      
      // Admins can write user data (for management)
      allow write: if request.auth != null && 
                      isAuthenticatedAdmin() && 
                      hasAdminPermission('users.manage');
    }
    
    // Content Collections (posts, pages, etc.)
    match /content/{contentId} {
      // Public read access for published content
      allow read: if resource.data.status == 'published';
      
      // Authenticated users can read all content
      allow read: if request.auth != null;
      
      // Content creators can write their own content
      allow write: if request.auth != null && 
                      (resource == null || 
                       resource.data.authorRef == 'users/' + request.auth.uid);
      
      // Admins can manage all content
      allow write: if request.auth != null && 
                      isAuthenticatedAdmin() && 
                      hasAdminPermission('content.manage');
    }
    
    // ========================================
    // HELPER FUNCTIONS
    // ========================================
    
    // Check if user is an authenticated admin
    function isAuthenticatedAdmin() {
      return request.auth != null && 
             request.auth.token != null && 
             (request.auth.token.admin == true || 
              request.auth.token.superAdmin == true);
    }
    
    // Check if admin has specific permission
    function hasAdminPermission(permission) {
      return request.auth != null && 
             request.auth.token != null && 
             (request.auth.token.permissions != null && 
              (permission in request.auth.token.permissions || 
               '*' in request.auth.token.permissions));
    }
    
    // Check if user is super admin
    function isSuperAdmin() {
      return request.auth != null && 
             request.auth.token != null && 
             request.auth.token.superAdmin == true;
    }
    
    // ========================================
    // FALLBACK RULES
    // ========================================
    
    // Default deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
