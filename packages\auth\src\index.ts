/**
 * @encreasl/auth - Shared Authentication Package
 * 
 * Provides Firebase authentication utilities, types, and React hooks
 * for the Encreasl monorepo. Built on Firebase Auth v10.
 */

// ========================================
// CORE EXPORTS
// ========================================

// Firebase configuration
export {
  initializeFirebase,
  getFirebaseAuth,
  createFirebaseConfig,
  validateFirebaseConfig,
  getCurrentFirebaseApp,
  getCurrentFirebaseAuth,
} from './firebase-config';

// Authentication service
export {
  AuthService,
  getAuthService,
  validateEmail,
  validatePassword,
} from './auth-service';

// React hooks
export {
  useAuth,
  useAdminAuth,
  useAuthState,
  useAdminAuthState,
} from './hooks';

// ========================================
// TYPE EXPORTS
// ========================================

export type {
  // Core types
  AuthError,
  AuthState,
  FirebaseConfig,
  
  // User profiles
  BaseUserProfile,
  AdminUserProfile,
  RegularUserProfile,
  
  // Authentication
  EmailPasswordCredentials,
  SignUpData,
  AuthMethods,
  
  // Hooks
  UseAuthReturn,
  UseAdminAuthReturn,
  
  // Events
  AuthEventType,
  AuthEvent,
  
  // Permissions
  AdminPermission,
  AdminRole,
  
  // Validation
  AuthValidation,
  
  // Firebase re-exports
  FirebaseUser,
  UserCredential,
} from './types';

// ========================================
// CONVENIENCE EXPORTS
// ========================================

// Re-export commonly used Firebase types
export type { 
  User as FirebaseUser, 
  UserCredential,
  Unsubscribe 
} from 'firebase/auth';
