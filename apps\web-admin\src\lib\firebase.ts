/**
 * Firebase Configuration for Admin App
 * Uses shared authentication package
 */

import { 
  initializeFirebase, 
  createFirebaseConfig, 
  validateFirebaseConfig,
  getFirebaseAuth 
} from '@encreasl/auth';
import { validateAdminClientEnv } from '@encreasl/env';

// ========================================
// ENVIRONMENT VALIDATION
// ========================================

const env = validateAdminClientEnv();

// ========================================
// FIREBASE INITIALIZATION
// ========================================

// Create Firebase config from environment variables
const firebaseConfig = createFirebaseConfig({
  NEXT_PUBLIC_FIREBASE_API_KEY: env.NEXT_PUBLIC_FIREBASE_API_KEY,
  NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  NEXT_PUBLIC_FIREBASE_PROJECT_ID: env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  NEXT_PUBLIC_FIREBASE_APP_ID: env.NEXT_PUBLIC_FIREBASE_APP_ID,
  NEXT_PUBLIC_FIREBASE_VAPID_KEY: env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
});

// Validate configuration
if (!validateFirebaseConfig(firebaseConfig)) {
  throw new Error('Invalid Firebase configuration. Please check your environment variables.');
}

// Initialize Firebase app
const app = initializeFirebase(firebaseConfig);

// Get Firebase Auth instance
export const auth = getFirebaseAuth(app);

// Export config for debugging
export { firebaseConfig };

// ========================================
// DEBUGGING UTILITIES
// ========================================

if (process.env.NODE_ENV === 'development') {
  console.log('🔥 Firebase initialized for admin app');
  console.log('📧 Auth domain:', firebaseConfig.authDomain);
  console.log('🆔 Project ID:', firebaseConfig.projectId);
}
