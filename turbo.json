{"$schema": "https://turbo.build/schema.json", "ui": "tui", "globalDependencies": [".env.local", ".env"], "globalEnv": ["NODE_ENV", "VERCEL_ENV", "CI"], "globalPassThroughEnv": ["NEXT_PUBLIC_*", "FIREBASE_*", "ADMIN_*"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**"], "inputs": ["$TURBO_DEFAULT$", ".env.local", ".env"], "env": ["NEXT_PUBLIC_*", "FIREBASE_*"]}, "dev": {"cache": false, "persistent": true, "env": ["NEXT_PUBLIC_*", "FIREBASE_*", "ADMIN_*"]}, "dev:turbo": {"cache": false, "persistent": true, "env": ["NEXT_PUBLIC_*", "FIREBASE_*", "ADMIN_*"]}, "lint": {"dependsOn": ["^lint"]}, "type-check": {"dependsOn": ["^type-check"]}}}