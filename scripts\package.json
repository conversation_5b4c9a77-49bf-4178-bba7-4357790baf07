{"name": "encreasl-admin-setup", "version": "1.0.0", "description": "Scripts to set up admin system in Firebase", "main": "createAdminUser.js", "scripts": {"setup-admin": "node createAdminUser.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"dotenv": "^17.2.0", "firebase": "^12.0.0", "firebase-admin": "^12.0.0", "google-auth-library": "^10.1.0"}, "keywords": ["firebase", "admin", "setup", "encreasl"], "author": "Encreasl Team", "license": "MIT"}