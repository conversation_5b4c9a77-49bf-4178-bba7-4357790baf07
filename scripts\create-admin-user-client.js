/**
 * Create Admin User Script (Client-side approach)
 * 
 * This script uses the Firebase Client SDK to create the admin user
 * instead of the Admin SDK to bypass permission issues.
 */

const { initializeApp } = require('firebase/app');
const { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword, signOut } = require('firebase/auth');
const { getFirestore, doc, setDoc, Timestamp } = require('firebase/firestore');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });

// Firebase Client configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

// Admin user details - YOUR REAL ADMIN ACCOUNT
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = '@Iamachessgrandmaster23';
const ADMIN_NAME = 'John - Admin';

async function createAdminUser() {
  try {
    console.log('🚀 Starting admin user creation (Client SDK approach)...');
    console.log('📊 Project ID:', firebaseConfig.projectId);

    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const auth = getAuth(app);
    const db = getFirestore(app);

    console.log('🔧 Creating admin user in Firebase Auth...');

    // Step 1: Create user with email and password
    let userCredential;
    try {
      userCredential = await createUserWithEmailAndPassword(auth, ADMIN_EMAIL, ADMIN_PASSWORD);
      console.log('✅ Created Firebase Auth user:', ADMIN_EMAIL);
    } catch (error) {
      if (error.code === 'auth/email-already-in-use') {
        console.log('ℹ️  User already exists, signing in to verify...');
        userCredential = await signInWithEmailAndPassword(auth, ADMIN_EMAIL, ADMIN_PASSWORD);
        console.log('✅ Verified existing user:', ADMIN_EMAIL);
      } else {
        throw error;
      }
    }

    const user = userCredential.user;
    console.log('👤 User UID:', user.uid);

    // Step 2: Create admin user document in Firestore
    console.log('🔧 Creating admin user document in Firestore...');
    
    const adminUserDoc = {
      id: user.uid,
      email: ADMIN_EMAIL,
      displayName: ADMIN_NAME,
      authProvider: 'email',
      emailVerified: user.emailVerified,
      
      // Role information
      roleRef: 'admin_roles/super-admin',
      roleName: 'Super Admin',
      permissions: ['*'],
      isActive: true,
      isSuperAdmin: true,
      
      // Profile information
      firstName: ADMIN_NAME.split(' ')[0],
      lastName: ADMIN_NAME.split(' ').slice(1).join(' '),
      title: 'Super Administrator',
      
      // Timestamps
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      lastLoginAt: Timestamp.now(),
      
      // Settings
      preferences: {
        theme: 'light',
        language: 'en',
        timezone: 'UTC',
        notifications: {
          email: true,
          push: true,
          desktop: true,
        }
      },
      
      // Security
      twoFactorEnabled: false,
      loginAttempts: 0,
      lockedUntil: null,
      
      // Metadata
      searchKeywords: [ADMIN_EMAIL, ADMIN_NAME.toLowerCase(), 'super', 'admin', 'administrator', 'john'],
    };

    await setDoc(doc(db, 'admin_users', user.uid), adminUserDoc);
    console.log('✅ Created admin user document in Firestore');

    // Step 3: Create audit log entry
    console.log('🔧 Creating audit log entry...');
    const auditLogDoc = {
      adminRef: `admin_users/${user.uid}`,
      adminEmail: ADMIN_EMAIL,
      adminName: ADMIN_NAME,
      action: 'user.create',
      resource: 'admin_users',
      description: 'Super admin user created via client SDK script',
      ipAddress: '127.0.0.1',
      userAgent: 'Client SDK Setup Script',
      timestamp: Timestamp.now(),
      severity: 'high',
      category: 'authentication',
      success: true,
      searchKeywords: ['user', 'create', 'admin', 'setup', 'john'],
      dateString: new Date().toISOString().split('T')[0]
    };

    // Generate a unique ID for the audit log
    const auditLogRef = doc(db, 'admin_audit_logs');
    await setDoc(auditLogRef, auditLogDoc);
    console.log('✅ Created audit log entry');

    // Step 4: Sign out
    await signOut(auth);
    console.log('✅ Signed out successfully');

    console.log('\n🎉 Admin user creation completed successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ Created/verified Firebase Auth user');
    console.log('✅ Created Firestore user document');
    console.log('✅ Created audit log entry');
    console.log('✅ Signed out');
    
    console.log('\n🔐 Login Credentials:');
    console.log('📧 Email:', ADMIN_EMAIL);
    console.log('🔑 Password:', ADMIN_PASSWORD);
    
    console.log('\n✨ You can now login to your admin app!');

  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    console.error('Error code:', error.code);
    console.error('Error message:', error.message);
    process.exit(1);
  }
}

// Run the script
createAdminUser()
  .then(() => {
    console.log('\n✨ Script completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
