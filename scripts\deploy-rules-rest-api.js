/**
 * Deploy Firestore Rules via Google Cloud REST API
 * 
 * This script bypasses Firebase Admin SDK limitations by using the
 * Google Cloud REST API directly to deploy Firestore security rules.
 */

const { GoogleAuth } = require('google-auth-library');
const crypto = require('crypto');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });

const PROJECT_ID = process.env.FIREBASE_ADMIN_PROJECT_ID;

// Production-ready Firestore rules
const FIRESTORE_RULES = `rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Admin Users Collection
    match /admin_users/{adminId} {
      // Allow authenticated users to create their own admin document during initial setup
      allow create: if request.auth != null && 
                       request.auth.uid == adminId &&
                       isValidAdminUserCreation();
      
      // Allow authenticated admins to read/write admin users
      allow read, write: if request.auth != null && 
                           (isAuthenticatedAdmin() || request.auth.uid == adminId);
      
      // Allow reading own profile
      allow read: if request.auth != null && 
                     request.auth.uid == adminId;
    }
    
    // Admin Audit Logs Collection
    match /admin_audit_logs/{logId} {
      // Allow authenticated users to create audit logs
      allow create: if request.auth != null;
      
      // Allow authenticated admins to read audit logs
      allow read: if request.auth != null && isAuthenticatedAdmin();
    }
    
    // System Configuration Collection
    match /system_config/{configId} {
      // Allow authenticated users to read/write system config
      allow read, write: if request.auth != null;
    }
    
    // Helper functions
    function isAuthenticatedAdmin() {
      return request.auth != null && 
             request.auth.token != null && 
             (request.auth.token.admin == true || 
              request.auth.token.superAdmin == true);
    }
    
    function isValidAdminUserCreation() {
      return request.resource.data.keys().hasAll(['email', 'displayName', 'isActive']) &&
             request.resource.data.email is string &&
             request.resource.data.displayName is string &&
             request.resource.data.isActive is bool;
    }
    
    // Default deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}`;

async function deployFirestoreRulesViaRestAPI() {
  try {
    console.log('🚀 Deploying Firestore rules via Google Cloud REST API...');
    console.log('📊 Project ID:', PROJECT_ID);

    // Initialize Google Auth with service account
    const auth = new GoogleAuth({
      credentials: {
        type: 'service_account',
        project_id: PROJECT_ID,
        private_key: process.env.FIREBASE_ADMIN_PRIVATE_KEY?.replace(/\\n/g, '\n'),
        client_email: process.env.FIREBASE_ADMIN_CLIENT_EMAIL,
      },
      scopes: [
        'https://www.googleapis.com/auth/cloud-platform',
        'https://www.googleapis.com/auth/firebase.rules'
      ]
    });

    const authClient = await auth.getClient();

    // Step 1: Create a ruleset
    console.log('🔧 Step 1: Creating ruleset...');
    
    // Generate fingerprint for the rules file
    const fingerprint = crypto.createHash('sha256').update(FIRESTORE_RULES).digest('base64');
    
    const createRulesetPayload = {
      source: {
        files: [
          {
            content: FIRESTORE_RULES,
            name: 'firestore.rules',
            fingerprint: fingerprint
          }
        ]
      }
    };

    const createRulesetUrl = `https://firebaserules.googleapis.com/v1/projects/${PROJECT_ID}/rulesets`;
    const createRulesetResponse = await authClient.request({
      url: createRulesetUrl,
      method: 'POST',
      data: createRulesetPayload
    });

    const rulesetName = createRulesetResponse.data.name;
    console.log('✅ Created ruleset:', rulesetName);

    // Step 2: Release the ruleset to Firestore
    console.log('🔧 Step 2: Releasing ruleset to Firestore...');
    
    const releasePayload = {
      name: `projects/${PROJECT_ID}/releases/cloud.firestore`,
      rulesetName: rulesetName
    };

    const releaseUrl = `https://firebaserules.googleapis.com/v1/projects/${PROJECT_ID}/releases/cloud.firestore`;
    const releaseResponse = await authClient.request({
      url: releaseUrl,
      method: 'PATCH',
      data: releasePayload
    });

    console.log('✅ Released ruleset to Firestore');

    // Step 3: Verify deployment
    console.log('🔧 Step 3: Verifying deployment...');
    
    const getReleaseUrl = `https://firebaserules.googleapis.com/v1/projects/${PROJECT_ID}/releases/cloud.firestore`;
    const getReleaseResponse = await authClient.request({
      url: getReleaseUrl,
      method: 'GET'
    });

    const currentRuleset = getReleaseResponse.data.rulesetName;
    console.log('✅ Current active ruleset:', currentRuleset);

    console.log('');
    console.log('🎉 FIRESTORE RULES DEPLOYED SUCCESSFULLY!');
    console.log('');
    console.log('📋 Deployment Summary:');
    console.log('✅ Rules compiled and validated');
    console.log('✅ Ruleset created:', rulesetName);
    console.log('✅ Rules deployed to Firestore');
    console.log('✅ Deployment verified');
    console.log('');
    console.log('🔒 Security Features Enabled:');
    console.log('✅ Admin user creation validation');
    console.log('✅ Role-based access control');
    console.log('✅ Audit log protection');
    console.log('✅ Default deny-all security');
    console.log('');
    console.log('🚀 Ready to run admin user creation script!');

  } catch (error) {
    console.error('❌ Failed to deploy Firestore rules:', error);
    
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    
    if (error.response?.status === 403) {
      console.error('');
      console.error('🔧 Permission Error:');
      console.error('The service account needs additional permissions.');
      console.error('Required roles:');
      console.error('- roles/firebaserules.admin');
      console.error('- roles/firebase.admin');
      console.error('- roles/serviceusage.serviceUsageConsumer');
    }
    
    process.exit(1);
  }
}

// Run the deployment
deployFirestoreRulesViaRestAPI()
  .then(() => {
    console.log('✨ Firestore rules deployment completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Firestore rules deployment failed:', error);
    process.exit(1);
  });
