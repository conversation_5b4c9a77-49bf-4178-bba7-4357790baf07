/**
 * React Hooks for Authentication
 * Based on Firebase Auth v10 and React best practices
 */

'use client';

import { useState, useEffect, useCallback } from 'react';
import { User as FirebaseUser } from 'firebase/auth';
import { getAuthService } from './auth-service';
import { 
  AuthError, 
  UseAuthReturn, 
  UseAdminAuthReturn,
  EmailPasswordCredentials,
  SignUpData,
  AdminUserProfile 
} from './types';

// ========================================
// BASE AUTHENTICATION HOOK
// ========================================

/**
 * Base authentication hook for all apps
 * Provides core authentication functionality
 */
export function useAuth(): UseAuthReturn {
  const [user, setUser] = useState<FirebaseUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<AuthError | null>(null);

  const authService = getAuthService();

  // Set up auth state listener
  useEffect(() => {
    const unsubscribe = authService.onAuthStateChanged((user) => {
      setUser(user);
      setLoading(false);
    });

    return unsubscribe;
  }, [authService]);

  // Clear error when user changes
  useEffect(() => {
    if (user) {
      setError(null);
    }
  }, [user]);

  // Authentication methods
  const signIn = useCallback(async (credentials: EmailPasswordCredentials) => {
    try {
      setError(null);
      setLoading(true);
      const result = await authService.signInWithEmailAndPassword(credentials);
      return result;
    } catch (err) {
      setError(err as AuthError);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [authService]);

  const signUp = useCallback(async (data: SignUpData) => {
    try {
      setError(null);
      setLoading(true);
      const result = await authService.signUpWithEmailAndPassword(data);
      return result;
    } catch (err) {
      setError(err as AuthError);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [authService]);

  const signOut = useCallback(async () => {
    try {
      setError(null);
      await authService.signOut();
    } catch (err) {
      setError(err as AuthError);
      throw err;
    }
  }, [authService]);

  const resetPassword = useCallback(async (email: string) => {
    try {
      setError(null);
      await authService.sendPasswordResetEmail(email);
    } catch (err) {
      setError(err as AuthError);
      throw err;
    }
  }, [authService]);

  return {
    user,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    resetPassword,
  };
}

// ========================================
// ADMIN AUTHENTICATION HOOK
// ========================================

/**
 * Admin authentication hook with additional admin-specific features
 * Extends base auth with admin profile and permissions
 */
export function useAdminAuth(): UseAdminAuthReturn {
  const baseAuth = useAuth();
  const [profile, setProfile] = useState<AdminUserProfile | null>(null);
  const [sessionValid, setSessionValid] = useState(true);

  const authService = getAuthService();

  // Load admin profile when user changes
  useEffect(() => {
    if (baseAuth.user) {
      loadAdminProfile(baseAuth.user);
    } else {
      setProfile(null);
    }
  }, [baseAuth.user]);

  // Session timeout check
  useEffect(() => {
    if (!baseAuth.user) return;

    const checkSession = () => {
      const lastActivity = localStorage.getItem('last_activity');
      if (!lastActivity) {
        updateLastActivity();
        return;
      }

      const now = Date.now();
      const sessionTimeout = 60 * 60 * 1000; // 1 hour default
      const isExpired = (now - parseInt(lastActivity)) > sessionTimeout;

      if (isExpired) {
        setSessionValid(false);
        authService.signOut();
      }
    };

    const interval = setInterval(checkSession, 60000); // Check every minute
    return () => clearInterval(interval);
  }, [baseAuth.user, authService]);

  // Update last activity on user interaction
  useEffect(() => {
    const updateActivity = () => updateLastActivity();
    
    window.addEventListener('click', updateActivity);
    window.addEventListener('keypress', updateActivity);
    window.addEventListener('scroll', updateActivity);

    return () => {
      window.removeEventListener('click', updateActivity);
      window.removeEventListener('keypress', updateActivity);
      window.removeEventListener('scroll', updateActivity);
    };
  }, []);

  const loadAdminProfile = async (user: FirebaseUser) => {
    try {
      // Get ID token to check custom claims
      const idTokenResult = await user.getIdTokenResult();
      const claims = idTokenResult.claims;

      // Create admin profile from user data and custom claims
      const adminProfile: AdminUserProfile = {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        photoURL: user.photoURL,
        emailVerified: user.emailVerified,
        createdAt: new Date(user.metadata.creationTime || Date.now()),
        lastLoginAt: new Date(user.metadata.lastSignInTime || Date.now()),
        isAdmin: claims.admin === true,
        isSuperAdmin: claims.superAdmin === true,
        role: claims.role || 'viewer',
        permissions: claims.permissions || [],
        department: claims.department,
        title: claims.title,
        mfaEnabled: false, // TODO: Implement MFA check
        sessionTimeout: claims.sessionTimeout || 3600,
      };

      setProfile(adminProfile);
    } catch (error) {
      console.error('Error loading admin profile:', error);
      setProfile(null);
    }
  };

  const hasPermission = useCallback((permission: string): boolean => {
    if (!profile) return false;
    if (profile.isSuperAdmin) return true;
    return profile.permissions.includes(permission) || profile.permissions.includes('*');
  }, [profile]);

  const updateLastActivity = () => {
    localStorage.setItem('last_activity', Date.now().toString());
  };

  return {
    ...baseAuth,
    profile,
    isAdmin: profile?.isAdmin || false,
    isSuperAdmin: profile?.isSuperAdmin || false,
    hasPermission,
    sessionValid,
  };
}

// ========================================
// UTILITY HOOKS
// ========================================

/**
 * Simple hook to check authentication status
 */
export function useAuthState() {
  const { user, loading } = useAuth();
  
  return {
    isAuthenticated: !!user,
    loading,
  };
}

/**
 * Hook for admin authentication status
 */
export function useAdminAuthState() {
  const { user, loading, isAdmin, sessionValid } = useAdminAuth();
  
  return {
    isAuthenticated: !!user && sessionValid,
    isAdmin: isAdmin && sessionValid,
    loading,
    sessionValid,
  };
}
