import type { NextConfig } from "next";
import { config } from 'dotenv';
import { resolve } from 'path';

// Load environment variables from multiple locations in the correct order
// This ensures environment variables are available before Next.js starts
const adminEnvPath = resolve(__dirname, '.env.local');
const rootEnvPath = resolve(__dirname, '../../.env.local');

console.log('🔍 Loading environment files:');
console.log('  Admin env path:', adminEnvPath);
console.log('  Root env path:', rootEnvPath);

// Load root env first (lower priority)
const rootResult = config({ path: rootEnvPath });
console.log('  Root env loaded:', rootResult.parsed ? Object.keys(rootResult.parsed).length : 0, 'variables');

// Load admin env second (higher priority, will override root)
const adminResult = config({ path: adminEnvPath });
console.log('  Admin env loaded:', adminResult.parsed ? Object.keys(adminResult.parsed).length : 0, 'variables');

// Load environment variables properly in monorepo setup
// This ensures Next.js can find .env.local files in both app and root directories
const nextConfig: NextConfig = {
  transpilePackages: ["@encreasl/ui", "@encreasl/auth", "@encreasl/env"],

  // Explicitly define environment variables for Next.js
  env: {
    // Firebase Client Configuration
    NEXT_PUBLIC_FIREBASE_API_KEY: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
    NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
    NEXT_PUBLIC_FIREBASE_PROJECT_ID: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
    NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
    NEXT_PUBLIC_FIREBASE_APP_ID: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
    NEXT_PUBLIC_FIREBASE_VAPID_KEY: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
  },

  turbopack: {
    resolveAlias: {
      "@/*": "./src/*",
    },
  },

  // Admin-specific security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
        ],
      },
    ];
  },

  // Experimental features for better environment variable handling
  experimental: {
    // Enable environment variable validation
    typedRoutes: true,
  },
};

export default nextConfig;
