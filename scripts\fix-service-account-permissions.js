/**
 * Fix Firebase Service Account Permissions
 * 
 * This script grants the necessary IAM permissions to the Firebase service account
 * so it can deploy Firestore security rules programmatically.
 */

const { GoogleAuth } = require('google-auth-library');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });

const PROJECT_ID = process.env.FIREBASE_ADMIN_PROJECT_ID;
const SERVICE_ACCOUNT_EMAIL = process.env.FIREBASE_ADMIN_CLIENT_EMAIL;

// Required roles for Firebase Admin SDK to deploy rules
const REQUIRED_ROLES = [
  'roles/serviceusage.serviceUsageConsumer',
  'roles/firebaserules.admin',
  'roles/firebase.admin',
  'roles/cloudsql.admin' // Sometimes needed for Firestore operations
];

async function grantServiceAccountPermissions() {
  try {
    console.log('🚀 Starting service account permission fix...');
    console.log('📊 Project ID:', PROJECT_ID);
    console.log('👤 Service Account:', SERVICE_ACCOUNT_EMAIL);

    // Initialize Google Auth
    const auth = new GoogleAuth({
      credentials: {
        type: 'service_account',
        project_id: PROJECT_ID,
        private_key: process.env.FIREBASE_ADMIN_PRIVATE_KEY?.replace(/\\n/g, '\n'),
        client_email: SERVICE_ACCOUNT_EMAIL,
      },
      scopes: [
        'https://www.googleapis.com/auth/cloud-platform',
        'https://www.googleapis.com/auth/iam'
      ]
    });

    const authClient = await auth.getClient();

    console.log('🔧 Granting required IAM permissions...');

    for (const role of REQUIRED_ROLES) {
      try {
        console.log(`  📝 Granting role: ${role}`);

        // Get current IAM policy
        const getPolicyUrl = `https://cloudresourcemanager.googleapis.com/v1/projects/${PROJECT_ID}:getIamPolicy`;
        const getPolicyResponse = await authClient.request({
          url: getPolicyUrl,
          method: 'POST',
          data: {}
        });

        const policy = getPolicyResponse.data;

        // Check if binding already exists
        let binding = policy.bindings?.find(b => b.role === role);
        
        if (!binding) {
          // Create new binding
          binding = {
            role: role,
            members: []
          };
          if (!policy.bindings) {
            policy.bindings = [];
          }
          policy.bindings.push(binding);
        }

        // Add service account to binding if not already present
        const memberString = `serviceAccount:${SERVICE_ACCOUNT_EMAIL}`;
        if (!binding.members.includes(memberString)) {
          binding.members.push(memberString);

          // Set updated IAM policy
          const setPolicyUrl = `https://cloudresourcemanager.googleapis.com/v1/projects/${PROJECT_ID}:setIamPolicy`;
          await authClient.request({
            url: setPolicyUrl,
            method: 'POST',
            data: {
              policy: policy
            }
          });

          console.log(`    ✅ Successfully granted ${role}`);
        } else {
          console.log(`    ℹ️  Role ${role} already granted`);
        }

      } catch (error) {
        if (error.response?.status === 403) {
          console.log(`    ❌ Permission denied for ${role} - you may need to grant this manually`);
        } else {
          console.log(`    ❌ Error granting ${role}:`, error.message);
        }
      }
    }

    console.log('');
    console.log('⏳ Waiting 30 seconds for permissions to propagate...');
    await new Promise(resolve => setTimeout(resolve, 30000));

    console.log('✅ Service account permissions updated successfully!');
    console.log('');
    console.log('📋 Summary:');
    console.log('✅ Service account has required permissions');
    console.log('✅ Permissions have propagated');
    console.log('✅ Ready to deploy Firestore rules');
    console.log('');
    console.log('🎉 You can now run the Firestore rules deployment script!');

  } catch (error) {
    console.error('❌ Error fixing service account permissions:', error);
    
    if (error.response?.status === 403) {
      console.error('');
      console.error('🔧 Manual Permission Fix Required:');
      console.error('1. Go to Google Cloud Console IAM page:');
      console.error(`   https://console.cloud.google.com/iam-admin/iam?project=${PROJECT_ID}`);
      console.error('');
      console.error('2. Find your service account:');
      console.error(`   ${SERVICE_ACCOUNT_EMAIL}`);
      console.error('');
      console.error('3. Add these roles:');
      REQUIRED_ROLES.forEach(role => {
        console.error(`   - ${role}`);
      });
      console.error('');
      console.error('4. Wait 2-3 minutes for propagation');
      console.error('5. Then run the Firestore rules deployment script');
    }
    
    process.exit(1);
  }
}

// Run the script
grantServiceAccountPermissions()
  .then(() => {
    console.log('✨ Permission fix completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Permission fix failed:', error);
    process.exit(1);
  });
