import { AuthGuard } from '@/components/AuthGuard';
import { AdminDashboard } from '@/components/AdminDashboard';

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AuthGuard requireAdmin={true}>
      <div className="min-h-screen bg-gray-50">
        <AdminDashboard>
          {children}
        </AdminDashboard>
      </div>
    </AuthGuard>
  );
}

export const metadata = {
  title: 'Admin Panel - Encreasl',
  description: 'Secure administrative panel for Encreasl CMS',
};
